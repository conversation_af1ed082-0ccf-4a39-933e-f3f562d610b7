Objective:
Verify whether Maxwell-like coupled dynamics exist between the Distortion Gradient Field (indicating local prediction error) and the Complexity Gradient Field (indicating local sensitivity to model parameters) during neural network training.

Step 1: Experimental Setup

Dataset:

Generate a synthetic 2D regression dataset:

Inputs: 2-dimensional, sampled uniformly from [-5, 5] × [-5, 5].

True underlying function: a nonlinear function, e.g., f(x, y) = sin(x) + cos(y).

Add Gaussian noise: mean=0, std=0.1.

Generate 500 training samples.

Model:

Small multilayer perceptron (MLP):

2 input neurons

Two hidden layers with 20 neurons each and ReLU activation.

1 output neuron (regression).

Training:

Optimizer: Adam, learning rate = 0.01.

Loss function: Mean Squared Error (MSE).

Train for 200 epochs, record model state every 10 epochs.

Step 2: Calculating the Fields

Distortion Gradient Field (vec_d):

At epochs {10, 50, 100, 150, 200}, compute:

Evaluate predictions on a fine 2D grid (e.g., 50×50) across the input space.

Compute local MSE at each grid point.

Numerically estimate gradients of this local distortion w.r.t. inputs (use finite differences).

Complexity Gradient Field (vec_c):

At the same epochs, compute:

For each grid point, calculate model output gradients w.r.t. model parameters.

Complexity at each point = squared L2-norm of these gradients.

Numerically estimate gradients of complexity across the 2D input grid (again finite differences).

Step 3: Compute Field Dynamics and Curls

For each epoch listed, compute:

The curl (scalar in 2D) of both fields across the grid:

curl(vec_d) = d/dx(vec_d_y) - d/dy(vec_d_x)

curl(vec_c) = d/dx(vec_c_y) - d/dy(vec_c_x)

Numerically compute the time derivatives of each field (finite differences between successive epochs).

Evaluate whether the proposed Maxwell-like equations hold approximately:

Equation 1: curl(vec_c) ≈ -α × (d(vec_d)/dt)

Equation 2: curl(vec_d) ≈ β × (d(vec_c)/dt)

Estimate suitable scaling factors α and β (via linear regression on the numerical data).

Step 4: Visualization

Clearly visualize at epochs {10, 50, 100, 150, 200}:

Vector fields (vec_d and vec_c) with quiver plots.

Scalar curl fields as heatmaps.

Temporal derivatives of fields as heatmaps.

Produce scatter plots of curl(vec_c) vs -(d(vec_d)/dt) and curl(vec_d) vs (d(vec_c)/dt).

Step 5: Reporting Requirements

Your output should include:

Python code clearly structured with readable functions for:

Data generation and model training

Computing distortion and complexity fields

Computing curls and temporal derivatives

Visualizations

A concise markdown summary containing:

Regression fits (scatter plots) quantifying how closely the curl of each field aligns with the time derivative of the other.

Estimates of scaling constants (α and β) from linear regression with confidence intervals.

Visual evidence (figures) clearly indicating any observable "learning wave" dynamics.

Explicit interpretation of results:

If fields follow Maxwell-like equations: clearly discuss implications for learning theory.

If not: discuss deviations and possible reasons.

Ensure the code is fully self-contained, clear, and produces reproducible, informative visual and numerical results suitable for rigorous interpretation.